{"HyAIAgent/test/test_phase4_integration.py::TestPhase4Integration::test_search_to_processing_pipeline": true, "HyAIAgent/test/test_phase4_integration.py::TestPhase4Integration::test_processing_to_analysis_workflow": true, "HyAIAgent/test/test_phase4_integration.py::TestPhase4Integration::test_multi_source_data_integration": true, "HyAIAgent/test/test_phase4_integration.py::TestPhase4Integration::test_hybrid_task_research_workflow": true, "HyAIAgent/test/test_phase4_integration.py::TestPhase4Integration::test_concurrent_hybrid_tasks": true, "HyAIAgent/test/test_phase4_integration.py::TestPhase4Integration::test_error_recovery_integration": true, "HyAIAgent/test/test_phase4_integration.py::TestPhase4Integration::test_prompt_template_integration": true, "HyAIAgent/test/test_phase4_integration.py::TestPhase4Integration::test_end_to_end_performance": true}