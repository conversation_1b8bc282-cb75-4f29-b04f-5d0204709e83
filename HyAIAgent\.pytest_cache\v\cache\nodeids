["test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_advanced_search_basic", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_advanced_search_regex", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_advanced_search_with_content", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_advanced_search_with_filters", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_batch_checksum_calculation", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_batch_content_processing", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_batch_copy_files", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_batch_delete_files", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_batch_move_files", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_batch_rename_files", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_file_filter", "test/test_advanced_search_and_batch.py::TestAdvancedSearchAndBatch::test_processing_stats", "test/test_advanced_widgets.py::TestAdvancedWidgets::test_advanced_widgets_initialization", "test/test_advanced_widgets.py::TestAdvancedWidgets::test_create_dynamic_layout", "test/test_advanced_widgets.py::TestAdvancedWidgets::test_create_interactive_chart", "test/test_advanced_widgets.py::TestAdvancedWidgets::test_create_multimedia_widget", "test/test_advanced_widgets.py::TestAdvancedWidgets::test_create_smart_input", "test/test_advanced_widgets.py::TestAdvancedWidgets::test_theme_management", "test/test_advanced_widgets.py::TestAdvancedWidgets::test_widget_stats", "test/test_advanced_widgets.py::TestDynamicLayoutWidget::test_add_remove_widget", "test/test_advanced_widgets.py::TestDynamicLayoutWidget::test_auto_adjust_layout", "test/test_advanced_widgets.py::TestDynamicLayoutWidget::test_dynamic_layout_initialization", "test/test_advanced_widgets.py::TestDynamicLayoutWidget::test_layout_type_switching", "test/test_advanced_widgets.py::TestInteractiveChartWidget::test_add_data_point", "test/test_advanced_widgets.py::TestInteractiveChartWidget::test_chart_initialization", "test/test_advanced_widgets.py::TestInteractiveChartWidget::test_chart_type_change", "test/test_advanced_widgets.py::TestInteractiveChartWidget::test_set_data", "test/test_advanced_widgets.py::TestMultimediaWidget::test_load_image", "test/test_advanced_widgets.py::TestMultimediaWidget::test_multimedia_initialization", "test/test_advanced_widgets.py::TestMultimediaWidget::test_supported_formats", "test/test_advanced_widgets.py::TestSmartInputWidget::test_signal_emission", "test/test_advanced_widgets.py::TestSmartInputWidget::test_smart_input_initialization", "test/test_advanced_widgets.py::TestSmartInputWidget::test_text_validation_email", "test/test_advanced_widgets.py::TestSmartInputWidget::test_text_validation_number", "test/test_advanced_widgets.py::TestThemeManager::test_available_themes", "test/test_advanced_widgets.py::TestThemeManager::test_get_theme", "test/test_advanced_widgets.py::TestThemeManager::test_theme_manager_initialization", "test/test_basic_search_operations.py::TestBasicSearchOperation::test_basic_search_operation_init", "test/test_basic_search_operations.py::TestBasicSearchOperation::test_execute_search_exception", "test/test_basic_search_operations.py::TestBasicSearchOperation::test_execute_search_invalid_params", "test/test_basic_search_operations.py::TestBasicSearchOperation::test_execute_search_success", "test/test_basic_search_operations.py::TestBasicSearchOperation::test_validate_params_invalid_search_depth", "test/test_basic_search_operations.py::TestBasicSearchOperation::test_validate_params_missing_query", "test/test_basic_search_operations.py::TestBasicSearchOperation::test_validate_params_success", "test/test_basic_search_operations.py::TestSearchToolkit::test_batch_search", "test/test_basic_search_operations.py::TestSearchToolkit::test_quick_search", "test/test_chart_generator.py::TestChartGenerator::test_apply_template", "test/test_chart_generator.py::TestChartGenerator::test_chart_info_and_deletion", "test/test_chart_generator.py::TestChartGenerator::test_create_chart_matplotlib", "test/test_chart_generator.py::TestChartGenerator::test_create_chart_plotly", "test/test_chart_generator.py::TestChartGenerator::test_export_chart", "test/test_chart_generator.py::TestChartGenerator::test_get_available_engines", "test/test_chart_generator.py::TestChartGenerator::test_get_supported_chart_types", "test/test_chart_generator.py::TestChartGenerator::test_init", "test/test_chart_generator.py::TestChartGenerator::test_select_engine", "test/test_chart_generator.py::TestChartGenerator::test_template_management", "test/test_chart_generator.py::TestChartGenerator::test_update_chart_data", "test/test_chart_generator.py::TestDataProcessor::test_aggregate_data", "test/test_chart_generator.py::TestDataProcessor::test_clean_data", "test/test_chart_generator.py::TestDataProcessor::test_convert_format", "test/test_config_processor.py::TestConfigProcessor::test_basic_config_processing", "test/test_config_processor.py::TestConfigProcessor::test_config_backup", "test/test_config_processor.py::TestConfigProcessor::test_config_format_conversion", "test/test_config_processor.py::TestConfigProcessor::test_config_merge", "test/test_config_processor.py::TestConfigProcessor::test_config_validation", "test/test_config_processor.py::TestConfigProcessor::test_config_validation_standalone", "test/test_config_processor.py::TestConfigProcessor::test_environment_variable_replacement", "test/test_config_processor.py::TestConfigProcessor::test_error_handling", "test/test_config_processor.py::TestConfigProcessor::test_format_detection", "test/test_config_processor.py::TestConfigProcessor::test_ini_config_processing", "test/test_config_processor.py::TestConfigProcessor::test_nested_key_operations", "test/test_config_processor.py::TestConfigProcessor::test_statistics_tracking", "test/test_config_processor.py::TestConfigProcessor::test_yaml_config_processing", "test/test_content_processor.py::TestContentProcessor::test_calculate_confidence_score", "test/test_content_processor.py::TestContentProcessor::test_calculate_quality_score", "test/test_content_processor.py::TestContentProcessor::test_clean_content", "test/test_content_processor.py::TestContentProcessor::test_clean_title", "test/test_content_processor.py::TestContentProcessor::test_count_words", "test/test_content_processor.py::TestContentProcessor::test_detect_content_type", "test/test_content_processor.py::TestContentProcessor::test_detect_language", "test/test_content_processor.py::TestContentProcessor::test_estimate_reading_time", "test/test_content_processor.py::TestContentProcessor::test_extract_entities", "test/test_content_processor.py::TestContentProcessor::test_extract_key_insights", "test/test_content_processor.py::TestContentProcessor::test_extract_keywords", "test/test_content_processor.py::TestContentProcessor::test_generate_overall_summary", "test/test_content_processor.py::TestContentProcessor::test_generate_summary", "test/test_content_processor.py::TestContentProcessor::test_get_processing_stats", "test/test_content_processor.py::TestContentProcessor::test_identify_related_topics", "test/test_content_processor.py::TestContentProcessor::test_initialization", "test/test_content_processor.py::TestContentProcessor::test_process_search_response", "test/test_content_processor.py::TestContentProcessor::test_process_single_result", "test/test_content_processor_comprehensive.py::TestContentProcessorComprehensive::test_batch_process_contents_comprehensive", "test/test_content_processor_comprehensive.py::TestContentProcessorComprehensive::test_data_format_normalization_comprehensive", "test/test_content_processor_comprehensive.py::TestContentProcessorComprehensive::test_data_relationships_comprehensive", "test/test_content_processor_comprehensive.py::TestContentProcessorComprehensive::test_data_structuring_entities_comprehensive", "test/test_content_processor_comprehensive.py::TestContentProcessorComprehensive::test_detect_duplicates_advanced", "test/test_content_processor_comprehensive.py::TestContentProcessorComprehensive::test_extract_key_information_comprehensive", "test/test_content_processor_comprehensive.py::TestContentProcessorComprehensive::test_generate_summary_multiple_contents", "test/test_content_processor_comprehensive.py::TestContentProcessorComprehensive::test_json_schema_conversion_comprehensive", "test/test_content_processor_comprehensive.py::TestContentProcessorComprehensive::test_process_search_response_integration", "test/test_content_processor_comprehensive.py::TestContentProcessorComprehensive::test_structure_data_comprehensive", "test/test_content_processor_comprehensive.py::TestContentProcessorComprehensive::test_validate_information_comprehensive", "test/test_content_processor_extended.py::TestContentProcessorExtended::test_batch_process_contents", "test/test_content_processor_extended.py::TestContentProcessorExtended::test_detect_duplicates", "test/test_content_processor_extended.py::TestContentProcessorExtended::test_empty_content_handling", "test/test_content_processor_extended.py::TestContentProcessorExtended::test_error_handling", "test/test_content_processor_extended.py::TestContentProcessorExtended::test_extract_key_information", "test/test_content_processor_extended.py::TestContentProcessorExtended::test_generate_summary", "test/test_content_processor_extended.py::TestContentProcessorExtended::test_similarity_calculation", "test/test_content_processor_extended.py::TestContentProcessorExtended::test_structure_data_list", "test/test_content_processor_extended.py::TestContentProcessorExtended::test_structure_data_table", "test/test_content_processor_step49.py::TestContentProcessorStep49::test_convert_to_json_schema_array", "test/test_content_processor_step49.py::TestContentProcessorStep49::test_convert_to_json_schema_object", "test/test_content_processor_step49.py::TestContentProcessorStep49::test_empty_content_handling", "test/test_content_processor_step49.py::TestContentProcessorStep49::test_extract_data_relationships_causal", "test/test_content_processor_step49.py::TestContentProcessorStep49::test_extract_data_relationships_hierarchical", "test/test_content_processor_step49.py::TestContentProcessorStep49::test_extract_structured_entities_dates_numbers", "test/test_content_processor_step49.py::TestContentProcessorStep49::test_extract_structured_entities_locations", "test/test_content_processor_step49.py::TestContentProcessorStep49::test_extract_structured_entities_organizations", "test/test_content_processor_step49.py::TestContentProcessorStep49::test_extract_structured_entities_persons", "test/test_content_processor_step49.py::TestContentProcessorStep49::test_normalize_data_format_csv", "test/test_content_processor_step49.py::TestContentProcessorStep49::test_normalize_data_format_json", "test/test_content_processor_step49.py::TestContentProcessorStep49::test_normalize_data_format_xml", "test/test_context_search.py::TestContextAnalyzer::test_analyze_conversation_context", "test/test_context_search.py::TestContextAnalyzer::test_extract_entities", "test/test_context_search.py::TestContextAnalyzer::test_identify_topics", "test/test_context_search.py::TestContextualSearchManager::test_cleanup_old_sessions", "test/test_context_search.py::TestContextualSearchManager::test_execute_contextual_search_basic", "test/test_context_search.py::TestContextualSearchManager::test_execute_contextual_search_with_conversation", "test/test_context_search.py::TestContextualSearchManager::test_execute_contextual_search_with_preferences", "test/test_context_search.py::TestContextualSearchManager::test_get_max_rounds_for_intent", "test/test_context_search.py::TestContextualSearchManager::test_search_history_management", "test/test_context_search.py::TestContextualSearchManager::test_select_search_strategy", "test/test_context_search.py::TestIntentClassifier::test_classify_comparison", "test/test_context_search.py::TestIntentClassifier::test_classify_information_seeking", "test/test_context_search.py::TestIntentClassifier::test_classify_news_update", "test/test_context_search.py::TestIntentClassifier::test_classify_problem_solving", "test/test_context_search.py::TestIntentClassifier::test_classify_tutorial", "test/test_context_search.py::TestIntentClassifier::test_classify_with_context", "test/test_data_integrator.py::TestDataIntegrator::test_confidence_score_calculation", "test/test_data_integrator.py::TestDataIntegrator::test_export_integrated_data", "test/test_data_integrator.py::TestDataIntegrator::test_get_integrated_data", "test/test_data_integrator.py::TestDataIntegrator::test_get_integration_statistics", "test/test_data_integrator.py::TestDataIntegrator::test_integrate_data_aggregate", "test/test_data_integrator.py::TestDataIntegrator::test_integrate_data_combine", "test/test_data_integrator.py::TestDataIntegrator::test_integrate_data_correlate", "test/test_data_integrator.py::TestDataIntegrator::test_integrate_data_deduplicate", "test/test_data_integrator.py::TestDataIntegrator::test_integrate_data_merge", "test/test_data_integrator.py::TestDataIntegrator::test_integration_with_invalid_sources", "test/test_data_integrator.py::TestDataIntegrator::test_quality_metrics_calculation", "test/test_data_integrator.py::TestDataIntegrator::test_register_data_source", "test/test_data_integrator.py::TestDataIntegrator::test_relationship_analysis", "test/test_data_integrator.py::TestDataIntegrator::test_remove_duplicates", "test/test_data_integrator.py::TestDataIntegrator::test_search_integrated_data", "test/test_data_integrator.py::TestDataIntegrator::test_search_with_filters", "test/test_data_integrator.py::TestDataIntegrator::test_source_reliability_evaluation", "test/test_data_integrator.py::TestDataIntegrator::test_text_similarity_calculation", "test/test_data_integrator.py::test_data_source_to_dict", "test/test_data_integrator.py::test_integrated_data_to_dict", "test/test_directory_operations.py::TestDirectoryOperations::test_copy_directory_overwrite", "test/test_directory_operations.py::TestDirectoryOperations::test_copy_directory_success", "test/test_directory_operations.py::TestDirectoryOperations::test_copy_directory_without_overwrite", "test/test_directory_operations.py::TestDirectoryOperations::test_create_directory_already_exists", "test/test_directory_operations.py::TestDirectoryOperations::test_create_directory_success", "test/test_directory_operations.py::TestDirectoryOperations::test_create_directory_with_parents", "test/test_directory_operations.py::TestDirectoryOperations::test_delete_directory_not_empty_without_recursive", "test/test_directory_operations.py::TestDirectoryOperations::test_delete_directory_recursive", "test/test_directory_operations.py::TestDirectoryOperations::test_delete_directory_success", "test/test_directory_operations.py::TestDirectoryOperations::test_delete_nonexistent_directory", "test/test_directory_operations.py::TestDirectoryOperations::test_get_directory_info_nonexistent", "test/test_directory_operations.py::TestDirectoryOperations::test_get_directory_info_success", "test/test_directory_operations.py::TestDirectoryOperations::test_get_operation_stats", "test/test_directory_operations.py::TestDirectoryOperations::test_list_directory_tree", "test/test_directory_operations.py::TestDirectoryOperations::test_move_directory_success", "test/test_directory_operations.py::TestDirectoryOperations::test_reset_stats", "test/test_document_processing_integration.py::TestDocumentProcessingIntegration::test_batch_document_processing_workflow", "test/test_document_processing_integration.py::TestDocumentProcessingIntegration::test_config_processor_format_converter_integration", "test/test_document_processing_integration.py::TestDocumentProcessingIntegration::test_document_processor_text_analyzer_integration", "test/test_document_processing_integration.py::TestDocumentProcessingIntegration::test_document_search_and_analysis", "test/test_document_processing_integration.py::TestDocumentProcessingIntegration::test_end_to_end_document_workflow", "test/test_document_processing_integration.py::TestDocumentProcessingIntegration::test_error_handling_integration", "test/test_document_processing_integration.py::TestDocumentProcessingIntegration::test_format_conversion_chain", "test/test_document_processing_integration.py::TestDocumentProcessingIntegration::test_statistics_integration", "test/test_document_processor.py::TestDocumentProcessor::test_batch_process_documents", "test/test_document_processor.py::TestDocumentProcessor::test_concurrent_processing", "test/test_document_processor.py::TestDocumentProcessor::test_document_metadata_extraction", "test/test_document_processor.py::TestDocumentProcessor::test_format_detection", "test/test_document_processor.py::TestDocumentProcessor::test_process_csv_document", "test/test_document_processor.py::TestDocumentProcessor::test_process_ini_document", "test/test_document_processor.py::TestDocumentProcessor::test_process_invalid_json", "test/test_document_processor.py::TestDocumentProcessor::test_process_json_document", "test/test_document_processor.py::TestDocumentProcessor::test_process_nonexistent_document", "test/test_document_processor.py::TestDocumentProcessor::test_process_text_document", "test/test_document_processor.py::TestDocumentProcessor::test_process_xml_document", "test/test_document_processor.py::TestDocumentProcessor::test_processing_stats", "test/test_document_processor.py::TestDocumentProcessor::test_search_case_sensitive", "test/test_document_processor.py::TestDocumentProcessor::test_search_in_documents", "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_config_file_structure", "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_file_security", "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_large_file", "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_markdown_file", "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_nonexistent_file", "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_python_file_structure", "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_single_file_basic", "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_single_file_content", "test/test_file_analyzer.py::TestFileAnalyzer::test_analyze_unsafe_file", "test/test_file_analyzer.py::TestFileAnalyzer::test_batch_analyze_files", "test/test_file_analyzer.py::TestFileAnalyzer::test_compare_files", "test/test_file_analyzer.py::TestFileAnalyzer::test_comprehensive_analysis", "test/test_file_analyzer.py::TestFileAnalyzer::test_concurrent_analysis", "test/test_file_analyzer.py::TestFileAnalyzer::test_find_similar_files", "test/test_file_analyzer.py::TestFileAnalyzer::test_get_analysis_stats", "test/test_file_analyzer.py::TestFileAnalyzer::test_reset_stats", "test/test_file_operations_integration.py::TestFileOperationsIntegration::test_complete_file_workflow", "test/test_file_operations_integration.py::TestFileOperationsIntegration::test_concurrent_operations", "test/test_file_operations_integration.py::TestFileOperationsIntegration::test_error_recovery", "test/test_file_operations_integration.py::TestFileOperationsIntegration::test_file_type_restrictions", "test/test_file_operations_integration.py::TestFileOperationsIntegration::test_file_utils_integration", "test/test_file_operations_integration.py::TestFileOperationsIntegration::test_large_file_handling", "test/test_file_operations_integration.py::TestFileOperationsIntegration::test_operation_statistics", "test/test_file_operations_integration.py::TestFileOperationsIntegration::test_path_utils_integration", "test/test_file_operations_integration.py::TestFileOperationsIntegration::test_security_integration", "test/test_format_converter.py::TestFormatConverter::test_batch_conversion", "test/test_format_converter.py::TestFormatConverter::test_csv_to_json_conversion", "test/test_format_converter.py::TestFormatConverter::test_encoding_conversion", "test/test_format_converter.py::TestFormatConverter::test_error_handling", "test/test_format_converter.py::TestFormatConverter::test_format_detection", "test/test_format_converter.py::TestFormatConverter::test_json_to_yaml_conversion", "test/test_format_converter.py::TestFormatConverter::test_statistics_tracking", "test/test_format_converter.py::TestFormatConverter::test_supported_formats", "test/test_format_converter.py::TestFormatConverter::test_text_file_conversion", "test/test_format_converter.py::TestFormatConverter::test_validation_functionality", "test/test_format_converter.py::TestFormatConverter::test_xml_dict_conversion", "test/test_format_converter.py::TestFormatConverter::test_xml_to_json_conversion", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_cancel_task", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_concurrent_task_execution", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_create_collect_and_integrate_task", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_create_research_and_analyze_task", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_create_search_and_save_task", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_data_integration_step", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_error_recovery_and_recommendations", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_execute_research_and_analyze_task", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_execute_search_and_save_task", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_get_execution_statistics", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_get_task_status", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_hybrid_task_to_dict", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_list_tasks", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_monitoring_task_execution", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_step_execution_with_dependencies", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_step_failure_handling", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_step_result_to_dict", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_step_retry_mechanism", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_task_step_to_dict", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_task_timeout_handling", "test/test_hybrid_task_executor.py::TestHybridTaskExecutor::test_validation_step", "test/test_information_verification.py::TestInformationVerification::test_assess_empty_information_quality", "test/test_information_verification.py::TestInformationVerification::test_assess_information_quality", "test/test_information_verification.py::TestInformationVerification::test_cross_validate_insufficient_sources", "test/test_information_verification.py::TestInformationVerification::test_cross_validate_sources", "test/test_information_verification.py::TestInformationVerification::test_validate_information_basic", "test/test_information_verification.py::TestInformationVerification::test_validate_information_comprehensive", "test/test_information_verification.py::TestInformationVerification::test_validation_statistics_update", "test/test_information_verification.py::TestInformationVerification::test_verify_empty_claims", "test/test_information_verification.py::TestInformationVerification::test_verify_factual_claims", "test/test_interaction_optimizer.py::TestContextAnalyzer::test_analyze_current_context", "test/test_interaction_optimizer.py::TestContextAnalyzer::test_get_context_history", "test/test_interaction_optimizer.py::TestContextAnalyzer::test_initialization", "test/test_interaction_optimizer.py::TestContextAnalyzer::test_update_context", "test/test_interaction_optimizer.py::TestDataModels::test_batch_operation_model", "test/test_interaction_optimizer.py::TestDataModels::test_quick_action_model", "test/test_interaction_optimizer.py::TestDataModels::test_smart_tip_model", "test/test_interaction_optimizer.py::TestInteractionOptimizer::test_add_quick_action", "test/test_interaction_optimizer.py::TestInteractionOptimizer::test_analyze_user_behavior", "test/test_interaction_optimizer.py::TestInteractionOptimizer::test_get_optimization_stats", "test/test_interaction_optimizer.py::TestInteractionOptimizer::test_initialization", "test/test_interaction_optimizer.py::TestInteractionOptimizer::test_optimize_performance", "test/test_interaction_optimizer.py::TestInteractionOptimizer::test_show_smart_tip", "test/test_interaction_optimizer.py::TestInteractionOptimizer::test_start_batch_operation", "test/test_interaction_optimizer.py::TestInteractionOptimizer::test_trigger_quick_action", "test/test_interaction_optimizer.py::TestQuickActionWidget::test_on_action_triggered", "test/test_interaction_optimizer.py::TestQuickActionWidget::test_refresh_actions", "test/test_interaction_optimizer.py::TestQuickActionWidget::test_widget_initialization", "test/test_knowledge_graph.py::TestKnowledgeGraph::test_add_duplicate_knowledge", "test/test_knowledge_graph.py::TestKnowledgeGraph::test_add_knowledge_success", "test/test_knowledge_graph.py::TestKnowledgeGraph::test_clear_cache", "test/test_knowledge_graph.py::TestKnowledgeGraph::test_get_statistics", "test/test_knowledge_graph.py::TestKnowledgeGraph::test_infer_relations_symmetry", "test/test_knowledge_graph.py::TestKnowledgeGraph::test_infer_relations_transitivity", "test/test_knowledge_graph.py::TestKnowledgeGraph::test_inference_rules_initialization", "test/test_knowledge_graph.py::TestKnowledgeGraph::test_knowledge_graph_initialization", "test/test_knowledge_graph.py::TestKnowledgeGraph::test_knowledge_triple_creation", "test/test_knowledge_graph.py::TestKnowledgeGraph::test_knowledge_triple_serialization", "test/test_knowledge_graph.py::TestKnowledgeGraph::test_query_knowledge_direct_match", "test/test_knowledge_graph.py::TestKnowledgeGraph::test_query_knowledge_with_inference", "test/test_knowledge_graph.py::TestKnowledgeGraph::test_storage_operations", "test/test_knowledge_graph.py::TestKnowledgeGraph::test_update_confidence", "test/test_multi_round_search.py::TestMultiRoundSearchManager::test_different_strategies", "test/test_multi_round_search.py::TestMultiRoundSearchManager::test_error_handling", "test/test_multi_round_search.py::TestMultiRoundSearchManager::test_execute_multi_round_search", "test/test_multi_round_search.py::TestMultiRoundSearchManager::test_execute_search_round", "test/test_multi_round_search.py::TestMultiRoundSearchManager::test_generate_session_id", "test/test_multi_round_search.py::TestMultiRoundSearchManager::test_get_strategy_params", "test/test_multi_round_search.py::TestMultiRoundSearchManager::test_initialization", "test/test_multi_round_search.py::TestMultiRoundSearchManager::test_session_management", "test/test_multi_round_search.py::TestQueryOptimizer::test_expand_query_related", "test/test_multi_round_search.py::TestQueryOptimizer::test_expand_query_specific", "test/test_multi_round_search.py::TestQueryOptimizer::test_expand_query_synonyms", "test/test_multi_round_search.py::TestQueryOptimizer::test_extract_keywords", "test/test_multi_round_search.py::TestQueryOptimizer::test_optimize_query", "test/test_multi_round_search.py::TestResultQualityEvaluator::test_empty_results_evaluation", "test/test_multi_round_search.py::TestResultQualityEvaluator::test_evaluate_authority", "test/test_multi_round_search.py::TestResultQualityEvaluator::test_evaluate_completeness", "test/test_multi_round_search.py::TestResultQualityEvaluator::test_evaluate_freshness", "test/test_multi_round_search.py::TestResultQualityEvaluator::test_evaluate_overall_quality", "test/test_multi_round_search.py::TestResultQualityEvaluator::test_evaluate_relevance", "test/test_reasoning_engine.py::TestReasoningEngine::test_causal_analysis", "test/test_reasoning_engine.py::TestReasoningEngine::test_confidence_calculation", "test/test_reasoning_engine.py::TestReasoningEngine::test_confidence_distribution", "test/test_reasoning_engine.py::TestReasoningEngine::test_error_handling", "test/test_reasoning_engine.py::TestReasoningEngine::test_hypothesis_testing", "test/test_reasoning_engine.py::TestReasoningEngine::test_logical_deduction", "test/test_reasoning_engine.py::TestReasoningEngine::test_multi_step_reasoning_success", "test/test_reasoning_engine.py::TestReasoningEngine::test_reasoning_chain_creation", "test/test_reasoning_engine.py::TestReasoningEngine::test_reasoning_engine_initialization", "test/test_reasoning_engine.py::TestReasoningEngine::test_reasoning_step_creation", "test/test_reasoning_engine.py::TestReasoningEngine::test_reasoning_step_validation", "test/test_reasoning_engine.py::TestReasoningEngine::test_reasoning_type_selection", "test/test_reasoning_engine.py::TestReasoningEngine::test_stats_management", "test/test_report_builder.py::TestReportBuilder::test_add_data_source", "test/test_report_builder.py::TestReportBuilder::test_create_report_config", "test/test_report_builder.py::TestReportBuilder::test_create_template", "test/test_report_builder.py::TestReportBuilder::test_delete_report_config", "test/test_report_builder.py::TestReportBuilder::test_fetch_api_data", "test/test_report_builder.py::TestReportBuilder::test_fetch_csv_data", "test/test_report_builder.py::TestReportBuilder::test_fetch_file_data", "test/test_report_builder.py::TestReportBuilder::test_fetch_json_data", "test/test_report_builder.py::TestReportBuilder::test_generate_report", "test/test_report_builder.py::TestReportBuilder::test_get_available_formats", "test/test_report_builder.py::TestReportBuilder::test_get_report_status", "test/test_report_builder.py::TestReportBuilder::test_get_template_variables", "test/test_report_builder.py::TestReportBuilder::test_init", "test/test_report_builder.py::TestReportBuilder::test_list_reports", "test/test_report_builder.py::TestReportBuilder::test_list_templates", "test/test_report_builder.py::TestReportBuilder::test_preview_report", "test/test_report_builder.py::TestReportBuilder::test_render_template_jinja2", "test/test_report_builder.py::TestReportBuilder::test_render_template_simple", "test/test_report_builder.py::TestReportBuilder::test_save_report_html", "test/test_report_builder.py::TestReportBuilder::test_save_report_txt", "test/test_report_builder.py::TestReportBuilder::test_schedule_report", "test/test_report_builder.py::TestReportBuilder::test_update_report_config", "test/test_report_builder.py::TestReportBuilder::test_validate_template", "test/test_search_cache.py::TestSearchCache::test_cache_clear", "test/test_search_cache.py::TestSearchCache::test_cache_entry_creation", "test/test_search_cache.py::TestSearchCache::test_cache_entry_expiration", "test/test_search_cache.py::TestSearchCache::test_cache_entry_serialization", "test/test_search_cache.py::TestSearchCache::test_cache_eviction", "test/test_search_cache.py::TestSearchCache::test_cache_expiration", "test/test_search_cache.py::TestSearchCache::test_cache_initialization", "test/test_search_cache.py::TestSearchCache::test_cache_miss", "test/test_search_cache.py::TestSearchCache::test_cache_stats", "test/test_search_cache.py::TestSearchCache::test_calculate_size", "test/test_search_cache.py::TestSearchCache::test_generate_cache_key", "test/test_search_cache.py::TestSearchCache::test_persistent_cache", "test/test_search_cache.py::TestSearchCache::test_processed_result_caching", "test/test_search_cache.py::TestSearchCache::test_search_result_caching", "test/test_search_functionality.py::TestSearchFunctionality::test_advanced_search_with_domain_filtering", "test/test_search_functionality.py::TestSearchFunctionality::test_batch_search_functionality", "test/test_search_functionality.py::TestSearchFunctionality::test_complete_search_workflow", "test/test_search_functionality.py::TestSearchFunctionality::test_search_caching_mechanism", "test/test_search_functionality.py::TestSearchFunctionality::test_search_error_handling", "test/test_search_functionality.py::TestSearchFunctionality::test_search_parameter_validation", "test/test_search_operations.py::TestSearchOperations::test_cache_expiration", "test/test_search_operations.py::TestSearchOperations::test_cache_key_generation", "test/test_search_operations.py::TestSearchOperations::test_cache_operations", "test/test_search_operations.py::TestSearchOperations::test_close", "test/test_search_operations.py::TestSearchOperations::test_initialization", "test/test_search_operations.py::TestSearchOperations::test_initialization_without_api_key", "test/test_search_operations.py::TestSearchOperations::test_search_stats", "test/test_search_operations.py::TestSearchOperations::test_search_web_with_cache", "test/test_search_operations.py::TestSearchOperations::test_search_web_with_mock", "test/test_search_operations.py::TestTavilySearchClient::test_client_initialization", "test/test_search_operations.py::TestTavilySearchClient::test_rate_limiting", "test/test_search_operations.py::TestTavilySearchClient::test_search_api_error", "test/test_search_operations.py::TestTavilySearchClient::test_search_success", "test/test_search_operations.py::TestTavilySearchClient::test_search_timeout", "test/test_settings_manager.py::TestSettingsDialog::test_dialog_initialization", "test/test_settings_manager.py::TestSettingsDialog::test_load_current_settings", "test/test_settings_manager.py::TestSettingsManager::test_activate_workspace_profile", "test/test_settings_manager.py::TestSettingsManager::test_create_workspace_profile", "test/test_settings_manager.py::TestSettingsManager::test_generate_recommendations", "test/test_settings_manager.py::TestSettingsManager::test_get_current_profile", "test/test_settings_manager.py::TestSettingsManager::test_get_preference", "test/test_settings_manager.py::TestSettingsManager::test_get_recommendations", "test/test_settings_manager.py::TestSettingsManager::test_get_settings_stats", "test/test_settings_manager.py::TestSettingsManager::test_get_workspace_profiles", "test/test_settings_manager.py::TestSettingsManager::test_initialization", "test/test_settings_manager.py::TestSettingsManager::test_learn_user_preference", "test/test_settings_manager.py::TestSettingsManager::test_sync_settings", "test/test_settings_manager.py::TestSettingsManager::test_update_profile_shortcuts", "test/test_stage5_basic_functionality.py::TestStage5BasicFunctionality::test_batch_kv_operations", "test/test_stage5_basic_functionality.py::TestStage5BasicFunctionality::test_config_manager_basic", "test/test_stage5_basic_functionality.py::TestStage5BasicFunctionality::test_error_handling", "test/test_stage5_basic_functionality.py::TestStage5BasicFunctionality::test_kv_store_basic", "test/test_stage5_basic_functionality.py::TestStage5BasicFunctionality::test_memory_usage_monitoring", "test/test_stage5_basic_functionality.py::TestStage5BasicFunctionality::test_performance_monitor_basic", "test/test_stage5_basic_functionality.py::TestStage5BasicFunctionality::test_results_summary", "test/test_stage5_comprehensive_functionality.py::TestStage5ComprehensiveFunctionality::test_advanced_reasoning_functionality", "test/test_stage5_comprehensive_functionality.py::TestStage5ComprehensiveFunctionality::test_creativity_engine_functionality", "test/test_stage5_comprehensive_functionality.py::TestStage5ComprehensiveFunctionality::test_knowledge_graph_functionality", "test/test_stage5_comprehensive_functionality.py::TestStage5ComprehensiveFunctionality::test_learning_system_functionality", "test/test_stage5_comprehensive_functionality.py::TestStage5ComprehensiveFunctionality::test_monitoring_functionality", "test/test_stage5_comprehensive_functionality.py::TestStage5ComprehensiveFunctionality::test_performance_summary", "test/test_stage5_comprehensive_functionality.py::TestStage5ComprehensiveFunctionality::test_tools_functionality", "test/test_stage5_comprehensive_functionality.py::TestStage5ComprehensiveFunctionality::test_ui_components_functionality", "test/test_stage5_core_functionality.py::TestStage5CoreFunctionality::test_batch_operations_performance", "test/test_stage5_core_functionality.py::TestStage5CoreFunctionality::test_chart_generator_functionality", "test/test_stage5_core_functionality.py::TestStage5CoreFunctionality::test_config_manager_functionality", "test/test_stage5_core_functionality.py::TestStage5CoreFunctionality::test_kv_store_functionality", "test/test_stage5_core_functionality.py::TestStage5CoreFunctionality::test_performance_monitor_functionality", "test/test_stage5_core_functionality.py::TestStage5CoreFunctionality::test_performance_summary", "test/test_system_integration.py::TestSystemIntegration::test_batch_task_execution", "test/test_system_integration.py::TestSystemIntegration::test_complex_workflow_execution", "test/test_system_integration.py::TestSystemIntegration::test_concurrent_task_execution", "test/test_system_integration.py::TestSystemIntegration::test_end_to_end_file_read_workflow", "test/test_system_integration.py::TestSystemIntegration::test_end_to_end_file_write_workflow", "test/test_system_integration.py::TestSystemIntegration::test_error_handling_and_recovery", "test/test_system_integration.py::TestSystemIntegration::test_performance_benchmarks", "test/test_system_integration.py::TestSystemIntegration::test_supported_operations_coverage", "test/test_task_integration.py::TestFileOperationTaskExecutor::test_file_read_task", "test/test_task_integration_simple.py::test_batch_operations", "test/test_task_integration_simple.py::test_create_file_operation_task", "test/test_task_integration_simple.py::test_file_operation_task_executor_basic", "test/test_task_integration_simple.py::test_task_system_integrator_basic", "test/test_text_analyzer.py::TestTextAnalyzer::test_analysis_options", "test/test_text_analyzer.py::TestTextAnalyzer::test_basic_text_analysis", "test/test_text_analyzer.py::TestTextAnalyzer::test_batch_analysis", "test/test_text_analyzer.py::TestTextAnalyzer::test_content_classification", "test/test_text_analyzer.py::TestTextAnalyzer::test_error_handling", "test/test_text_analyzer.py::TestTextAnalyzer::test_file_analysis", "test/test_text_analyzer.py::TestTextAnalyzer::test_language_detection", "test/test_text_analyzer.py::TestTextAnalyzer::test_pattern_extraction", "test/test_text_analyzer.py::TestTextAnalyzer::test_quality_assessment", "test/test_text_analyzer.py::TestTextAnalyzer::test_statistics_tracking", "test/test_text_analyzer.py::TestTextAnalyzer::test_structure_analysis", "test/test_text_analyzer.py::TestTextAnalyzer::test_word_frequency_analysis", "test/test_visualization.py::TestDataRelationshipChart::test_chart_type_change", "test/test_visualization.py::TestDataRelationshipChart::test_data_chart_initialization", "test/test_visualization.py::TestDataRelationshipChart::test_set_data", "test/test_visualization.py::TestKnowledgeGraphVisualizer::test_add_edge", "test/test_visualization.py::TestKnowledgeGraphVisualizer::test_add_node", "test/test_visualization.py::TestKnowledgeGraphVisualizer::test_knowledge_graph_initialization", "test/test_visualization.py::TestKnowledgeGraphVisualizer::test_layout_change", "test/test_visualization.py::TestKnowledgeGraphVisualizer::test_remove_node", "test/test_visualization.py::TestKnowledgeGraphVisualizer::test_set_graph_data", "test/test_visualization.py::TestPerformanceMonitor::test_alert_thresholds", "test/test_visualization.py::TestPerformanceMonitor::test_clear_data", "test/test_visualization.py::TestPerformanceMonitor::test_performance_monitor_initialization", "test/test_visualization.py::TestPerformanceMonitor::test_start_stop_monitoring", "test/test_visualization.py::TestPerformanceMonitor::test_update_interval", "test/test_visualization.py::TestPerformanceMonitor::test_update_metrics_with_mock", "test/test_visualization.py::TestTaskFlowVisualizer::test_add_task", "test/test_visualization.py::TestTaskFlowVisualizer::test_set_connections", "test/test_visualization.py::TestTaskFlowVisualizer::test_set_tasks", "test/test_visualization.py::TestTaskFlowVisualizer::test_task_flow_initialization", "test/test_visualization.py::TestTaskFlowVisualizer::test_update_task_status", "test/test_visualization.py::TestTaskFlowVisualizer::test_view_type_change", "test/test_visualization.py::TestVisualization::test_get_current_visualization_type", "test/test_visualization.py::TestVisualization::test_get_visualization_stats", "test/test_visualization.py::TestVisualization::test_performance_monitoring_control", "test/test_visualization.py::TestVisualization::test_set_data_relationship_data", "test/test_visualization.py::TestVisualization::test_set_knowledge_graph_data", "test/test_visualization.py::TestVisualization::test_set_task_flow_data", "test/test_visualization.py::TestVisualization::test_switch_to_visualization", "test/test_visualization.py::TestVisualization::test_visualization_initialization", "test/test_workflow_engine.py::TestWorkflowEngine::test_add_task", "test/test_workflow_engine.py::TestWorkflowEngine::test_cancel_execution", "test/test_workflow_engine.py::TestWorkflowEngine::test_check_conditions", "test/test_workflow_engine.py::TestWorkflowEngine::test_check_dependencies", "test/test_workflow_engine.py::TestWorkflowEngine::test_create_workflow", "test/test_workflow_engine.py::TestWorkflowEngine::test_evaluate_condition", "test/test_workflow_engine.py::TestWorkflowEngine::test_execute_workflow", "test/test_workflow_engine.py::TestWorkflowEngine::test_find_start_tasks", "test/test_workflow_engine.py::TestWorkflowEngine::test_get_execution_status", "test/test_workflow_engine.py::TestWorkflowEngine::test_get_workflow_statistics", "test/test_workflow_engine.py::TestWorkflowEngine::test_handle_delay_task", "test/test_workflow_engine.py::TestWorkflowEngine::test_handle_function_task", "test/test_workflow_engine.py::TestWorkflowEngine::test_handle_http_request_task", "test/test_workflow_engine.py::TestWorkflowEngine::test_has_circular_dependency", "test/test_workflow_engine.py::TestWorkflowEngine::test_init", "test/test_workflow_engine.py::TestWorkflowEngine::test_list_workflows", "test/test_workflow_engine.py::TestWorkflowEngine::test_remove_task", "test/test_workflow_engine.py::TestWorkflowEngine::test_remove_task_with_dependencies", "test/test_workflow_engine.py::TestWorkflowEngine::test_resolve_parameters", "test/test_workflow_engine.py::TestWorkflowEngine::test_resolve_string", "test/test_workflow_engine.py::TestWorkflowEngine::test_validate_workflow"]